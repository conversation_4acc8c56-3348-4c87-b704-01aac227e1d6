[workspace]
members = [
  # Archives
  "file-formats/archives/wow-mpq",

  # World Data
  "file-formats/world-data/wow-adt",
  "file-formats/world-data/wow-wdl",
  "file-formats/world-data/wow-wdt",

  # Graphics
  "file-formats/graphics/wow-blp",
  "file-formats/graphics/wow-m2",
  "file-formats/graphics/wow-wmo",

  # Database
  "file-formats/database/wow-cdbc",

  # FFI
  "ffi/storm-ffi",

  # CLI
  "warcraft-rs",
]
resolver = "2"

[workspace.package]
version = "0.3.1"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2024"
rust-version = "1.86"
license = "MIT OR Apache-2.0"
repository = "https://github.com/wowemulation-dev/warcraft-rs"
homepage = "https://github.com/wowemulation-dev/warcraft-rs"

[workspace.dependencies]
# Error handling
thiserror = "2.0"
anyhow = "1.0"

# Logging
log = "0.4"
env_logger = "0.11"

# Data structures and utilities
rand = "0.9"
tempfile = "3.20"
bitflags = "2.9"
bytes = "1.10"
memchr = "2.7"
glam = "0.30"
memmap2 = "0.9.5"
rayon = "1.10"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml_ng = "0.10"
binrw = "0.15.0"
csv = "1.3"

# CLI utilities
clap = { version = "4.5", features = ["derive"] }
colored = "3.0"
indicatif = "0.17"

# FFI
libc = "0.2.172"

# Development
criterion = { version = "0.6", features = ["html_reports"] }
pretty_assertions = "1.4"
proptest = "1.7"
assert_cmd = "2.0"
predicates = "3.1"
test-case = "3.3"
