[package]
name = "warcraft-rs"
version = "0.3.1"
authors.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
repository.workspace = true
homepage.workspace = true
description = "Unified CLI for World of Warcraft file format parsing, conversion, and validation"
keywords = ["wow", "warcraft", "mpq", "blp", "dbc"]
categories = ["command-line-utilities", "game-development"]

[dependencies]
# CLI framework
clap = { version = "4.5", features = ["derive", "cargo", "env"] }
clap_complete = "4.5"

# File format crates
wow-mpq = { path = "../file-formats/archives/wow-mpq", version = "0.3.0" }
wow-cdbc = { path = "../file-formats/database/wow-cdbc", version = "0.3.0", optional = true, features = [
  "cli",
  "mmap",
  "yaml",
] }
wow-blp = { path = "../file-formats/graphics/wow-blp", version = "0.3.0", optional = true }
wow-m2 = { path = "../file-formats/graphics/wow-m2", version = "0.3.0", optional = true }
wow-wmo = { path = "../file-formats/graphics/wow-wmo", version = "0.3.0", optional = true }
wow-adt = { path = "../file-formats/world-data/wow-adt", version = "0.3.0", optional = true }
wow-wdt = { path = "../file-formats/world-data/wow-wdt", version = "0.3.0", optional = true }
wow-wdl = { path = "../file-formats/world-data/wow-wdl", version = "0.3.0", optional = true }

# Error handling and logging
anyhow = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }

# UI and formatting
indicatif = { workspace = true }
prettytable-rs = "0.10"
humansize = "2.1"
console = "0.15"

# Utilities
chrono = "0.4"
glob = "0.3"

# Image processing (for BLP)
image = { version = "0.25", optional = true }

# Optional parallel processing
rayon = { version = "1.10", optional = true }

# Optional serialization for data export
serde = { workspace = true, optional = true }
serde_json = { workspace = true, optional = true }
serde_yaml_ng = { version = "0.10", optional = true }

[features]
default = ["mpq", "dbc", "blp", "m2", "wmo", "adt", "wdt", "wdl"]
full = [
  "mpq",
  "dbc",
  "blp",
  "m2",
  "wmo",
  "adt",
  "wdt",
  "wdl",
  "serde",
  "extract",
  "parallel",
  "yaml",
]
mpq = []
dbc = ["dep:wow-cdbc"]
blp = ["dep:wow-blp", "dep:image"]
m2 = ["dep:wow-m2"]
wmo = ["dep:wow-wmo"]
adt = ["dep:wow-adt"]
wdt = ["dep:wow-wdt", "serde"]
wdl = ["dep:wow-wdl"]
serde = ["dep:serde", "dep:serde_json"]
extract = ["wow-adt?/extract"]
parallel = ["wow-adt?/parallel", "dep:rayon"]
yaml = ["dbc", "serde", "dep:serde_yaml_ng"]

[[bin]]
name = "warcraft-rs"
path = "src/main.rs"

[dev-dependencies]
assert_cmd = { workspace = true }
predicates = { workspace = true }
tempfile = { workspace = true }
