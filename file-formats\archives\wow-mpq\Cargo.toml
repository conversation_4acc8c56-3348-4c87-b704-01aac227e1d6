[package]
name = "wow-mpq"
version = "0.3.1"
authors.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true
homepage.workspace = true
readme = "README.md"
description = "High-performance parser for World of Warcraft MPQ archives with parallel processing support"
categories = ["game-development", "parser-implementations"]
keywords = ["wow", "warcraft", "mpq", "archive", "gamedev"]

[dependencies]
# Error handling
thiserror = { workspace = true }

# Logging
log = { workspace = true }

# Hashing and cryptography
sha1 = "0.10"
md-5 = "0.10"
crc32fast = "1.4"
adler = "1.0"
rsa = "0.9"
num-bigint = "0.4"
num-traits = "0.2"

# Compression algorithms
flate2 = "1.1"
bzip2 = "0.6"
lzma-rs = "0.3"
pklib = "0.1"
implode = "0.1.1"

# Data structures and utilities
bytes = "1.10"
byteorder = "1.5"
rand = { workspace = true }
tempfile = { workspace = true }

# Parallel processing
rayon = "1.10"

# Database support
rusqlite = { version = "0.32", features = ["bundled"] }
directories = "5.0"
chrono = "0.4"
glob = "0.3"


[dev-dependencies]
criterion = { workspace = true }
env_logger = { workspace = true }
pretty_assertions = { workspace = true }
proptest = { workspace = true }
tempfile = { workspace = true }
assert_cmd = { workspace = true }
predicates = { workspace = true }

[[bench]]
name = "hash"
harness = false

[[bench]]
name = "crypto"
harness = false

[[bench]]
name = "compression"
harness = false

[[bench]]
name = "builder"
harness = false

[[bench]]
name = "archive_creation"
harness = false

[[bench]]
name = "archive_extraction"
harness = false

[[bench]]
name = "parallel_processing"
harness = false

[[bench]]
name = "single_archive_parallel"
harness = false

[features]
default = []

# Utilities for testing and examples with WoW game data
test-utils = []

# Enable all features for docs.rs
[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[[example]]
name = "analyze_attributes"

[[example]]
name = "bulk_modify"

[[example]]
name = "compare_archives"

[[example]]
name = "create_archive"


[[example]]
name = "list_archive_contents"

[[example]]
name = "list_with_names"

[[example]]
name = "modify_archive"


[[example]]
name = "simple_list"

[[example]]
name = "single_archive_parallel_demo"

[[example]]
name = "verify_wow_files"
required-features = ["test-utils"]

