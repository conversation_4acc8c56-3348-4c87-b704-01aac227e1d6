[package]
name = "wow-wdl"
version = "0.3.1"
authors.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true
homepage.workspace = true
documentation = "https://docs.rs/wow-wdl"
description = "Parser for World of Warcraft WDL (World Detail Level) low-resolution terrain files"
readme = "README.md"
keywords = ["wow", "warcraft", "wdl", "terrain", "gamedev"]
categories = ["game-development", "parser-implementations"]

[dependencies]
thiserror = { workspace = true }
memchr = "2.7"

[dev-dependencies]
criterion = { workspace = true }

[features]
default = []

[[bench]]
name = "parser_benchmark"
harness = false
