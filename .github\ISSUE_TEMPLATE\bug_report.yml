name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug]: "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! 
        Please provide as much information as possible to help us reproduce and fix the issue.

  - type: checkboxes
    id: prerequisites
    attributes:
      label: Prerequisites
      description: Please confirm you have completed the following
      options:
        - label: I have searched existing issues to ensure this isn't a duplicate
          required: true
        - label: I have read the [Contributing Guidelines](../CONTRIBUTING.md)
          required: true
        - label: I am using the latest version or main branch
          required: false

  - type: textarea
    id: bug-description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe what happened...
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: What you expected to happen
      placeholder: Describe what you expected...
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual Behavior
      description: What actually happened
      placeholder: Describe what actually happened...
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Run command `...`
        2. With input file `...`
        3. See error...
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of warcraft-rs are you using?
      placeholder: "v0.1.0 or commit hash"
    validations:
      required: true

  - type: dropdown
    id: operating-system
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - Linux
        - macOS  
        - Windows
        - Other (please specify in additional context)
    validations:
      required: true

  - type: input
    id: rust-version
    attributes:
      label: Rust Version
      description: What version of Rust are you using? (Run `rustc --version`)
      placeholder: "rustc 1.86.0"
    validations:
      required: false

  - type: textarea
    id: command-output
    attributes:
      label: Command Output
      description: Please paste the full command output, including any error messages
      render: shell
      placeholder: |
        $ cargo run -- mpq list example.mpq
        Error: Failed to open archive
        ...
    validations:
      required: false

  - type: textarea
    id: file-details
    attributes:
      label: File Details
      description: If the issue is with a specific file, please provide details
      placeholder: |
        - File type: MPQ/DBC/BLP/etc.
        - File size: 
        - WoW version: 1.12.1/2.4.3/3.3.5a/etc.
        - Source: Original Blizzard files/modified/etc.
    validations:
      required: false

  - type: textarea
    id: logs
    attributes:
      label: Logs
      description: Please paste any relevant log output (use `RUST_LOG=debug` for verbose logs)
      render: shell
      placeholder: |
        Set RUST_LOG=debug and paste output here...
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any other context about the problem
      placeholder: |
        - Does this happen with all files or just specific ones?
        - Any workarounds you've found?
        - Related issues or discussions?
    validations:
      required: false

  - type: checkboxes
    id: component
    attributes:
      label: Affected Components
      description: Which components are affected? (Check all that apply)
      options:
        - label: MPQ archives
        - label: DBC/DB2 databases  
        - label: BLP textures
        - label: M2 models
        - label: WMO world objects
        - label: ADT terrain
        - label: WDT/WDL world data
        - label: CLI interface
        - label: FFI/C bindings
        - label: Documentation
        - label: Build system