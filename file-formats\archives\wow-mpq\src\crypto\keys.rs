//! Key generation and encryption table

/// Generate the MPQ encryption table at compile time
const fn generate_encryption_table() -> [u32; 0x500] {
    let mut table = [0u32; 0x500];
    let mut seed: u32 = 0x00100001;

    let mut index1 = 0;
    while index1 < 0x100 {
        let mut index2 = 0;
        while index2 < 5 {
            let table_index = index1 + index2 * 0x100;

            // Update seed using the algorithm
            seed = seed.wrapping_mul(125).wrapping_add(3) % 0x2AAAAB;
            let temp1 = (seed & 0xFFFF) << 0x10;

            seed = seed.wrapping_mul(125).wrapping_add(3) % 0x2AAAAB;
            let temp2 = seed & 0xFFFF;

            table[table_index] = temp1 | temp2;
            index2 += 1;
        }
        index1 += 1;
    }

    table
}

/// The static encryption table used by all MPQ operations
pub const ENCRYPTION_TABLE: [u32; 0x500] = generate_encryption_table();

/// ASCII uppercase conversion table
pub(crate) const ASCII_TO_UPPER: [u8; 256] = [
    // 0x00-0x0F
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
    // 0x10-0x1F
    0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
    // 0x20-0x2F
    0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
    // 0x30-0x3F
    0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
    // 0x40-0x4F
    0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F,
    // 0x50-0x5F
    0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F,
    // 0x60-0x6F: a-o -> A-O
    0x60, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F,
    // 0x70-0x7F: p-z -> P-Z
    0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F,
    // 0x80-0x8F
    0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F,
    // 0x90-0x9F
    0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0x9B, 0x9C, 0x9D, 0x9E, 0x9F,
    // 0xA0-0xAF
    0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9, 0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF,
    // 0xB0-0xBF
    0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF,
    // 0xC0-0xCF
    0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xCB, 0xCC, 0xCD, 0xCE, 0xCF,
    // 0xD0-0xDF
    0xD0, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xDB, 0xDC, 0xDD, 0xDE, 0xDF,
    // 0xE0-0xEF
    0xE0, 0xE1, 0xE2, 0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA, 0xEB, 0xEC, 0xED, 0xEE, 0xEF,
    // 0xF0-0xFF
    0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF,
];

/// ASCII lowercase conversion table
pub(crate) const ASCII_TO_LOWER: [u8; 256] = [
    // 0x00-0x0F
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
    // 0x10-0x1F
    0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
    // 0x20-0x2F
    0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
    // 0x30-0x3F
    0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
    // 0x40-0x4F: @, A-O -> @, a-o
    0x40, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F,
    // 0x50-0x5F: P-Z -> p-z
    0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F,
    // 0x60-0x6F
    0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F,
    // 0x70-0x7F
    0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F,
    // 0x80-0x8F
    0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F,
    // 0x90-0x9F
    0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0x9B, 0x9C, 0x9D, 0x9E, 0x9F,
    // 0xA0-0xAF
    0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9, 0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF,
    // 0xB0-0xBF
    0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF,
    // 0xC0-0xCF
    0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xCB, 0xCC, 0xCD, 0xCE, 0xCF,
    // 0xD0-0xDF
    0xD0, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xDB, 0xDC, 0xDD, 0xDE, 0xDF,
    // 0xE0-0xEF
    0xE0, 0xE1, 0xE2, 0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA, 0xEB, 0xEC, 0xED, 0xEE, 0xEF,
    // 0xF0-0xFF
    0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF,
];

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encryption_table_generation() {
        // Test known values from the encryption table
        // These values are from the MPQ specification
        assert_eq!(ENCRYPTION_TABLE[0x000], 0x55C6_36E2);
        assert_eq!(ENCRYPTION_TABLE[0x001], 0x02BE_0170);
        assert_eq!(ENCRYPTION_TABLE[0x002], 0x584B_71D4);
        assert_eq!(ENCRYPTION_TABLE[0x003], 0x2984_F00E);
        assert_eq!(ENCRYPTION_TABLE[0x004], 0xB682_C809);

        // Test some middle values
        assert_eq!(ENCRYPTION_TABLE[0x100], 0x76F8_C1B1);
        assert_eq!(ENCRYPTION_TABLE[0x200], 0x3DF6_965D);
        assert_eq!(ENCRYPTION_TABLE[0x300], 0x15F2_61D3);
        assert_eq!(ENCRYPTION_TABLE[0x400], 0x193A_A698);

        // Test last few values
        assert_eq!(ENCRYPTION_TABLE[0x4FB], 0x6149_809C);
        assert_eq!(ENCRYPTION_TABLE[0x4FC], 0xB009_9EF4);
        assert_eq!(ENCRYPTION_TABLE[0x4FD], 0xC5F6_53A5);
        assert_eq!(ENCRYPTION_TABLE[0x4FE], 0x4C10_790D);
        assert_eq!(ENCRYPTION_TABLE[0x4FF], 0x7303_286C);
    }

    #[test]
    fn test_ascii_tables() {
        // Test uppercase conversion
        assert_eq!(ASCII_TO_UPPER[b'a' as usize], b'A');
        assert_eq!(ASCII_TO_UPPER[b'z' as usize], b'Z');
        assert_eq!(ASCII_TO_UPPER[b'A' as usize], b'A');
        assert_eq!(ASCII_TO_UPPER[b'0' as usize], b'0');
        assert_eq!(ASCII_TO_UPPER[b' ' as usize], b' ');

        // Test lowercase conversion
        assert_eq!(ASCII_TO_LOWER[b'A' as usize], b'a');
        assert_eq!(ASCII_TO_LOWER[b'Z' as usize], b'z');
        assert_eq!(ASCII_TO_LOWER[b'a' as usize], b'a');
        assert_eq!(ASCII_TO_LOWER[b'0' as usize], b'0');
        assert_eq!(ASCII_TO_LOWER[b' ' as usize], b' ');
    }
}
