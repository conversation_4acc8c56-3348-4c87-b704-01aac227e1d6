---
name: Release CLI (Ephemeral)

on:
  workflow_call:
    inputs:
      version:
        description: 'Version to release'
        required: true
        type: string
      dry-run:
        description: 'Dry run (no actual release)'
        required: false
        type: boolean
        default: false

  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 0.1.0)'
        required: true
        type: string
      dry-run:
        description: 'Dry run (no actual release)'
        required: false
        type: boolean
        default: false

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  RUSTFLAGS: -D warnings
  CARGO_INCREMENTAL: 0

# Prevent concurrent CLI releases
concurrency:
  group: release-cli-${{ github.repository }}
  cancel-in-progress: false

permissions:
  contents: write
  packages: write

jobs:
  generate-ephemeral-keys:
    name: Generate Ephemeral Signing Keys
    runs-on: ubuntu-latest
    outputs:
      key-id: ${{ steps.generate.outputs.key-id }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
      
      - name: Generate ephemeral keypair
        id: generate
        env:
          AGE_KEY_PUBLIC: ${{ vars.AGE_KEY_PUBLIC }}
        run: |
          ./ephemeral-gen.sh
          
          # Extract key ID for output
          KEY_ID=$(head -1 minisign.pub | sed 's/.*: //')
          echo "key-id=${KEY_ID}" >> $GITHUB_OUTPUT
      
      - name: Upload ephemeral keys
        uses: actions/upload-artifact@v4
        with:
          name: ephemeral-keys
          path: |
            minisign.pub
            minisign.key.age
          retention-days: 1

  build-binaries:
    name: Build ${{ matrix.target }}
    needs: generate-ephemeral-keys
    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux
          - target: x86_64-unknown-linux-gnu
            os: ubuntu-latest
            use_cross: true
            archive: tar.gz
          - target: x86_64-unknown-linux-musl
            os: ubuntu-latest
            use_cross: true
            archive: tar.gz
          - target: aarch64-unknown-linux-gnu
            os: ubuntu-latest
            use_cross: true
            archive: tar.gz
          - target: aarch64-unknown-linux-musl
            os: ubuntu-latest
            use_cross: true
            archive: tar.gz
          - target: armv7-unknown-linux-musleabihf
            os: ubuntu-latest
            use_cross: true
            archive: tar.gz

          # Windows
          - target: x86_64-pc-windows-msvc
            os: windows-latest
            use_cross: false
            archive: zip
          - target: aarch64-pc-windows-msvc
            os: windows-latest
            use_cross: false
            archive: zip

          # macOS
          - target: x86_64-apple-darwin
            os: macos-latest
            use_cross: false
            archive: tar.gz
          - target: aarch64-apple-darwin
            os: macos-latest
            use_cross: false
            archive: tar.gz

    runs-on: ${{ matrix.os }}
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Rust
        uses: dtolnay/rust-toolchain@v1
        with:
          toolchain: stable
          targets: ${{ matrix.target }}
      
      - name: Install cross for cross-compilation
        if: matrix.use_cross
        uses: taiki-e/install-action@v2
        with:
          tool: cross
      
      - name: Download ephemeral keys
        uses: actions/download-artifact@v4
        with:
          name: ephemeral-keys
      
      - name: Setup signing tools
        run: |
          cargo install rsign2
          cargo install rage
      
      - name: Build binary
        shell: bash
        run: |
          if [ "${{ matrix.use_cross }}" = "true" ]; then
            cross build --release --package warcraft-rs --bin warcraft-rs --target ${{ matrix.target }}
          else
            cargo build --release --package warcraft-rs --bin warcraft-rs --target ${{ matrix.target }}
          fi
      
      - name: Package binary (Unix)
        if: runner.os != 'Windows'
        run: |
          cd target/${{ matrix.target }}/release
          tar czf ../../../warcraft-rs-${{ inputs.version }}-${{ matrix.target }}.tar.gz warcraft-rs
          cd ../../../
      
      - name: Package binary (Windows)
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          cd target/${{ matrix.target }}/release
          Compress-Archive -Path warcraft-rs.exe -DestinationPath ../../../warcraft-rs-${{ inputs.version }}-${{ matrix.target }}.zip
          cd ../../../
      
      - name: Sign packages
        env:
          AGE_KEY_SECRET: ${{ secrets.AGE_KEY_SECRET }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_RUN_ID: ${{ github.run_id }}
        shell: bash
        run: |
          if [[ "${{ runner.os }}" == "Windows" ]]; then
            bash ./ephemeral-sign.sh warcraft-rs-${{ inputs.version }}-${{ matrix.target }}.zip
          else
            ./ephemeral-sign.sh warcraft-rs-${{ inputs.version }}-${{ matrix.target }}.${{ matrix.archive }}
          fi
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: binaries-${{ matrix.target }}
          path: |
            warcraft-rs-*.tar.gz
            warcraft-rs-*.tar.gz.sig
            warcraft-rs-*.zip
            warcraft-rs-*.zip.sig
            minisign.pub

  create-release:
    name: Create Release
    needs: [generate-ephemeral-keys, build-binaries]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: Prepare release files
        run: |
          mkdir -p release
          
          # Move all binaries and signatures
          find artifacts -name "warcraft-rs-*" -type f -exec mv {} release/ \;
          
          # Copy public key from any artifact (they're all the same)
          cp artifacts/binaries-*/minisign.pub release/ 2>/dev/null || cp artifacts/ephemeral-keys/minisign.pub release/
          
          # Create checksums
          cd release
          sha256sum warcraft-rs-*.{tar.gz,zip} > SHA256SUMS 2>/dev/null || true
          cd ..
          
          # List release contents
          ls -la release/
      
      - name: Create release notes
        run: |
          cat > release/RELEASE_NOTES.md << 'EOF'
          ## warcraft-rs v${{ inputs.version }}
          
          ### Verification
          
          This release uses ephemeral signing keys. To verify:
          
          1. Download the binary, signature (`.sig`), and public key (`minisign.pub`)
          2. Verify using minisign or rsign:
          
          ```bash
          # Using minisign
          minisign -V -p minisign.pub -m warcraft-rs-${{ inputs.version }}-<target>.tar.gz
          
          # Using rsign
          rsign verify -p minisign.pub warcraft-rs-${{ inputs.version }}-<target>.tar.gz
          ```
          
          ### Ephemeral Key ID
          
          This release was signed with ephemeral key: `${{ needs.generate-ephemeral-keys.outputs.key-id }}`
          
          ### Installation
          
          #### Unix-like systems
          ```bash
          curl -fsSL https://raw.githubusercontent.com/wowemulation-dev/warcraft-rs/main/install.sh | bash
          ```
          
          #### Windows PowerShell
          ```powershell
          irm https://raw.githubusercontent.com/wowemulation-dev/warcraft-rs/main/install.ps1 | iex
          ```
          
          ### Supported Platforms
          
          - Linux: x86_64, aarch64, armv7 (GNU and musl)
          - Windows: x86_64, aarch64 (MSVC)
          - macOS: x86_64, aarch64
          
          ### Checksums
          
          SHA256 checksums are available in the `SHA256SUMS` file.
          EOF
      
      - name: Create GitHub Release
        if: ${{ !inputs.dry-run }}
        uses: softprops/action-gh-release@v2
        with:
          tag_name: warcraft-rs-v${{ inputs.version }}
          name: warcraft-rs v${{ inputs.version }}
          body_path: release/RELEASE_NOTES.md
          files: |
            release/warcraft-rs-*.tar.gz
            release/warcraft-rs-*.tar.gz.sig
            release/warcraft-rs-*.zip
            release/warcraft-rs-*.zip.sig
            release/minisign.pub
            release/SHA256SUMS
      
      - name: Dry run summary
        if: ${{ inputs.dry-run }}
        run: |
          echo "=== DRY RUN SUMMARY ==="
          echo "Would have created release: warcraft-rs v${{ inputs.version }}"
          echo ""
          echo "Files that would be uploaded:"
          ls -la release/
          echo ""
          echo "Release notes:"
          cat release/RELEASE_NOTES.md