[package]
name = "wow-adt"
version = "0.3.1"
authors.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true
homepage.workspace = true
documentation = "https://docs.rs/wow-adt"
description = "Parser for World of Warcraft ADT terrain files with heightmap and texture layer support"
readme = "README.md"
keywords = ["wow", "warcraft", "adt", "terrain", "gamedev"]
categories = ["game-development", "parser-implementations"]

[dependencies]
thiserror = { workspace = true }
log = "0.4"
image = { version = "0.25", optional = true }
rayon = { version = "1.10", optional = true }
memmap2 = { version = "0.9", optional = true }

[dev-dependencies]
criterion = "0.6"
tempfile = "3.20"
pretty_assertions = "1.4"

[features]
default = []
extract = ["dep:image"]
parallel = ["dep:rayon"]
mmap = ["dep:memmap2"]
image = ["dep:image"]

[[bench]]
name = "parser_benchmark"
harness = false
