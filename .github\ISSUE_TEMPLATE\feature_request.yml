name: 🚀 Feature Request
description: Suggest a new feature or enhancement
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! 
        Please provide detailed information to help us understand and evaluate your request.

  - type: checkboxes
    id: prerequisites
    attributes:
      label: Prerequisites
      description: Please confirm you have completed the following
      options:
        - label: I have searched existing issues to ensure this isn't a duplicate
          required: true
        - label: I have read the [Contributing Guidelines](../CONTRIBUTING.md)
          required: true
        - label: I have checked the [project roadmap](../README.md) and documentation
          required: false

  - type: textarea
    id: problem-statement
    attributes:
      label: Problem Statement
      description: What problem does this feature solve? What's the motivation?
      placeholder: |
        Describe the problem you're trying to solve...
        Is this related to a frustration with existing functionality?
    validations:
      required: true

  - type: textarea
    id: proposed-solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented
      placeholder: |
        A clear and concise description of what you want to happen...
        How should this feature work?
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe alternative solutions or features you've considered
      placeholder: |
        What other approaches could solve this problem?
        Why is your proposed solution the best?
    validations:
      required: false

  - type: dropdown
    id: feature-type
    attributes:
      label: Feature Type
      description: What type of feature is this?
      options:
        - New file format support
        - CLI enhancement
        - API improvement
        - Performance optimization
        - Developer experience
        - Documentation
        - Tooling/build system
        - FFI/language bindings
        - Other
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would improve my workflow
        - High - Blocking my use case
        - Critical - Required for production use
    validations:
      required: true

  - type: checkboxes
    id: affected-components
    attributes:
      label: Affected Components
      description: Which components would this feature affect? (Check all that apply)
      options:
        - label: MPQ archives
        - label: DBC/DB2 databases
        - label: BLP textures
        - label: M2 models
        - label: WMO world objects
        - label: ADT terrain
        - label: WDT/WDL world data
        - label: CLI interface
        - label: FFI/C bindings
        - label: Documentation
        - label: Build system
        - label: Cross-cutting/multiple components

  - type: textarea
    id: use-cases
    attributes:
      label: Use Cases
      description: Describe specific use cases for this feature
      placeholder: |
        1. As a game modder, I want to...
        2. As a developer, I need to...
        3. When working with X files, I expect to...
    validations:
      required: true

  - type: textarea
    id: technical-considerations
    attributes:
      label: Technical Considerations
      description: Any technical constraints, dependencies, or implementation details to consider
      placeholder: |
        - Performance implications
        - Breaking changes
        - External dependencies
        - Compatibility requirements
        - Security considerations
    validations:
      required: false

  - type: textarea
    id: examples
    attributes:
      label: Examples
      description: Provide examples of how this feature would be used
      placeholder: |
        ```bash
        # Example command usage
        cargo run -- new-feature --option value
        ```
        
        ```rust
        // Example API usage
        let result = api.new_method()?;
        ```
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any other context, screenshots, or references
      placeholder: |
        - Links to relevant documentation
        - References to similar features in other tools
        - Screenshots or mockups
        - Related issues or discussions
    validations:
      required: false

  - type: checkboxes
    id: implementation-willingness
    attributes:
      label: Implementation
      description: Are you willing to help implement this feature?
      options:
        - label: I'm willing to implement this feature myself
        - label: I can help with testing and feedback
        - label: I can provide domain expertise/consultation
        - label: I can help with documentation
        - label: I'm looking for someone else to implement this