[package]
name = "wow-wmo"
version = "0.3.1"
authors.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true
homepage.workspace = true
readme = "README.md"
description = "Parser, editor, and converter for World of Warcraft WMO (World Model Object) files"
categories = ["game-development", "parser-implementations"]
keywords = ["wow", "warcraft", "wmo", "parser", "gamedev"]

[dependencies]
# Error handling
thiserror = { workspace = true }

# Data structures
bitflags = { workspace = true }

# Logging
tracing = "0.1.41"
tracing-subscriber = "0.3.19"

[dev-dependencies]
criterion = { workspace = true }
tempfile = { workspace = true }
rstest = "0.25.0"
test-case = "3.3.1"

[[bench]]
name = "parser_benchmarks"
harness = false
