---
name: Install Script Test

on:
  # Test when install script changes
  pull_request:
    branches: [main]
    paths:
      - 'install.sh'
      - '.github/workflows/install-script.yml'

  # Test after releases are created
  release:
    types: [published]

  # Allow manual testing
  workflow_dispatch:

jobs:
  shellcheck:
    name: ShellCheck
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run ShellCheck on install script
        uses: ludeeus/action-shellcheck@2.0.0
        with:
          scandir: '.'
          format: gcc
          severity: warning

  test-install:
    name: Test Install (${{ matrix.os }})
    needs: [shellcheck]
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Test install script functionality (PR/manual)
        if: github.event_name != 'release'
        shell: bash
        run: |
          # Make script executable
          chmod +x install.sh

          # Test help
          ./install.sh --help

          # Test platform detection function
          bash -c 'source ./install.sh && detect_platform'

          # Test script syntax
          bash -n install.sh

      - name: Test actual installation (release)
        if: github.event_name == 'release'
        shell: bash
        run: |
          # Make script executable
          chmod +x install.sh

          # Test actual installation of the released version
          ./install.sh --tag ${{ github.event.release.tag_name }}

          # Verify installation
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            ~/.warcraft-rs/bin/warcraft-rs.exe --version
          else
            ~/.warcraft-rs/bin/warcraft-rs --version
          fi

