name: 🔒 Security Vulnerability
description: Report a security vulnerability (use private disclosure)
title: "[Security]: "
labels: ["security", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        # ⚠️ IMPORTANT: Security Vulnerability Reporting
        
        **Please DO NOT report security vulnerabilities using public GitHub issues.**
        
        For security vulnerabilities, please use our private disclosure process:
        
        1. **Email**: Send details to `<EMAIL>`
        2. **Subject**: Include "SECURITY: warcraft-rs vulnerability"
        3. **Encryption**: Use PGP if possible (key available on request)
        
        We will acknowledge receipt within 48 hours and provide a detailed response within 7 days.
        
        ## Our Security Policy
        
        Please review our full [Security Policy](https://github.com/wowemulation-dev/.github/blob/main/SECURITY.md) for:
        - Supported versions
        - Vulnerability disclosure timeline
        - Coordination process
        - Credit and acknowledgments
        
        ## When to Use Private Disclosure
        
        Use private disclosure for vulnerabilities that could:
        - Allow unauthorized access to systems
        - Enable data exposure or corruption  
        - Cause denial of service
        - Bypass security controls
        - Enable privilege escalation
        - Allow code execution

  - type: checkboxes
    id: acknowledgment
    attributes:
      label: Security Reporting Acknowledgment
      description: Please confirm you understand our security reporting process
      options:
        - label: I understand this form is for documentation only and will report the actual vulnerability privately
          required: true
        - label: I have read the Security Policy
          required: true
        - label: I will not publicly disclose details until coordinated disclosure
          required: true

  - type: dropdown
    id: severity
    attributes:
      label: Estimated Severity
      description: What is your initial assessment of the severity?
      options:
        - Low - Limited impact, difficult to exploit
        - Medium - Moderate impact or easier to exploit
        - High - Significant impact and exploitable
        - Critical - Severe impact, easily exploitable
        - Unknown - Need help assessing severity
    validations:
      required: true

  - type: textarea
    id: vulnerability-summary
    attributes:
      label: Vulnerability Summary
      description: Brief, high-level description (avoid technical details)
      placeholder: |
        Provide a general description without specific exploitation details...
        Focus on the type of vulnerability rather than exact steps.
    validations:
      required: true

  - type: checkboxes
    id: affected-components
    attributes:
      label: Affected Components
      description: Which components are potentially affected?
      options:
        - label: MPQ archive parsing
        - label: DBC/DB2 database parsing
        - label: BLP texture processing
        - label: M2 model parsing
        - label: WMO object parsing
        - label: ADT terrain parsing
        - label: Compression/decompression
        - label: Cryptographic functions
        - label: File I/O operations
        - label: CLI interface
        - label: FFI/C bindings
        - label: Build system/dependencies
        - label: Unknown/multiple components

  - type: dropdown
    id: attack-vector
    attributes:
      label: Attack Vector
      description: How could this vulnerability be exploited?
      options:
        - Malicious file input
        - Network-based attack
        - Local privilege escalation
        - Memory corruption
        - Logic flaw
        - Cryptographic weakness
        - Dependency vulnerability
        - Other/Unknown
    validations:
      required: false

  - type: textarea
    id: impact-description
    attributes:
      label: Potential Impact
      description: What could an attacker achieve? (general terms only)
      placeholder: |
        Describe potential consequences without providing exploitation details...
        - Information disclosure
        - System compromise
        - Denial of service
        - Data corruption
        - etc.
    validations:
      required: true

  - type: textarea
    id: affected-versions
    attributes:
      label: Affected Versions
      description: Which versions are affected?
      placeholder: |
        - All versions
        - v0.1.0 and later
        - Specific version range
        - Main branch only
        - Unknown
    validations:
      required: false

  - type: textarea
    id: discovery-context
    attributes:
      label: Discovery Context
      description: How was this vulnerability discovered?
      placeholder: |
        - Security research
        - Fuzzing/testing
        - User report
        - Code review
        - Automated scanning
        - Incident response
    validations:
      required: false

  - type: checkboxes
    id: disclosure-timeline
    attributes:
      label: Disclosure Preferences
      description: What are your preferences for handling this vulnerability?
      options:
        - label: I prefer coordinated disclosure with a reasonable timeline
        - label: I am willing to delay public disclosure for a fix
        - label: I would like to be credited for the discovery (if desired)
        - label: I can provide additional testing or verification
        - label: I need this resolved urgently due to active exploitation

  - type: textarea
    id: contact-info
    attributes:
      label: Secure Contact Information
      description: How can we securely contact you for follow-up?
      placeholder: |
        - Email address (preferably with PGP key)
        - Signal/encrypted messaging
        - Other secure communication method
        
        Note: This will be visible in the public issue. Use only information
        you're comfortable sharing publicly for initial contact.
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ## Next Steps
        
        After submitting this form:
        
        1. **Send detailed vulnerability information privately** to `<EMAIL>`
        2. **Reference this issue number** in your private communication
        3. **Wait for acknowledgment** (within 48 hours)
        4. **Participate in coordinated disclosure** process
        
        Thank you for helping keep warcraft-rs secure! 🙏