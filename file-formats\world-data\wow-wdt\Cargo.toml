[package]
name = "wow-wdt"
version = "0.3.1"
authors.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
repository.workspace = true
homepage.workspace = true
description = "Parser, validator, and converter for World of Warcraft WDT (World Data Table) files"
keywords = ["wow", "wdt", "world-of-warcraft", "game", "parser"]
categories = ["game-development", "parser-implementations"]

[dependencies]
# Core dependencies
thiserror = { workspace = true }
bitflags = { workspace = true }

# For examples
anyhow = { workspace = true }

# Serialization support (optional)
serde = { workspace = true, features = ["derive"], optional = true }
serde_json = { workspace = true, optional = true }

[dev-dependencies]
tempfile = { workspace = true }
pretty_assertions = "1.4"
criterion = { workspace = true }

[features]
default = []
serde = ["dep:serde", "dep:serde_json"]

[lib]
name = "wow_wdt"
path = "src/lib.rs"

[[bench]]
name = "parser_benchmark"
harness = false
