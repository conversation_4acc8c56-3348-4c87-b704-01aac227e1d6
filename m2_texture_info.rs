use std::env;
use std::path::Path;
use wow_m2::model::M2Model;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();
    
    if args.len() < 2 {
        println!("Usage: {} <path_to_m2_file>", args[0]);
        println!("Example: {} DemonForm.M2", args[0]);
        return Ok(());
    }
    
    let m2_path = &args[1];
    
    println!("🎮 M2 Model Texture Analysis");
    println!("============================");
    println!("Loading: {}", m2_path);
    
    // 加载 M2 模型
    let model = M2Model::load(m2_path)?;
    
    // 显示基本信息
    println!("\n📋 Basic Information:");
    println!("  Model name: {:?}", model.name.as_deref().unwrap_or("Unknown"));
    println!("  Version: {:?}", model.header.version());
    println!("  Total textures: {}", model.textures.len());
    
    // 显示纹理信息
    println!("\n🎨 Texture Information:");
    if model.textures.is_empty() {
        println!("  No textures found in this model.");
    } else {
        for (i, texture) in model.textures.iter().enumerate() {
            println!("  Texture {}:", i);
            println!("    Type: {:?}", texture.texture_type);
            println!("    Flags: {:?}", texture.flags);
            
            // 获取文件名
            if !texture.filename.is_empty() {
                let filename = texture.filename.string.to_string();
                if !filename.trim().is_empty() {
                    println!("    Filename: {}", filename);
                    
                    // 检查文件是否存在
                    let m2_dir = Path::new(m2_path).parent().unwrap_or(Path::new("."));
                    let texture_path = m2_dir.join(&filename);
                    if texture_path.exists() {
                        println!("    Status: ✅ File exists");
                    } else {
                        println!("    Status: ❌ File not found at {}", texture_path.display());
                    }
                } else {
                    println!("    Filename: (empty - hardcoded texture)");
                }
            } else {
                println!("    Filename: (hardcoded texture)");
            }
            println!();
        }
    }
    
    // 显示其他相关信息
    println!("📊 Additional Information:");
    println!("  Vertices: {}", model.vertices.len());
    println!("  Bones: {}", model.bones.len());
    println!("  Animations: {}", model.animations.len());
    println!("  Materials: {}", model.materials.len());
    
    Ok(())
}
