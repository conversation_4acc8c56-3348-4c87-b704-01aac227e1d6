# 🔗 External Links

Helpful resources for working with World of Warcraft file formats.

## Community Documentation

### wowdev.wiki

The primary community resource for WoW file format documentation:

- [wowdev.wiki Homepage](https://wowdev.wiki/)
- [ADT Format](https://wowdev.wiki/ADT)
- [M2 Format](https://wowdev.wiki/M2)
- [WMO Format](https://wowdev.wiki/WMO)
- [BLP Format](https://wowdev.wiki/BLP)
- [DBC Format](https://wowdev.wiki/DBC)
- [MPQ Format](https://wowdev.wiki/MPQ)

## Tools and Libraries

### StormLib (MPQ)

- [StormLib GitHub](https://github.com/ladislav-zezula/StormLib) - C++ MPQ
  library (reference implementation)
- Note: `wow-mpq` has 98.75% compatibility with StormLib archives

### BLP format

- [BLPConverter](https://www.wowinterface.com/downloads/info14110-BLPConverter.html) - BLP texture converter

### WoW Model Viewer

- [WoW Model Viewer](https://wowmodelviewer.net/) - 3D model viewer
- [WMV GitHub](https://github.com/WowModelViewer/wowmodelviewer) - Source code

### 010 Editor Templates

- [010 Editor](https://www.sweetscape.com/010editor/) - Binary editor

## Open Source Projects

### Trinity Core

- [TrinityCore](https://www.trinitycore.org/) - WoW server emulator
- [GitHub Repository](https://github.com/TrinityCore/TrinityCore) - Source code
- [DBC Structures](https://github.com/TrinityCore/TrinityCore/tree/master/src/server/game/DataStores) - DBC definitions

### Mangos

- [getMaNGOS](https://getmangos.eu/) - WoW server emulator
- [GitHub Organization](https://github.com/mangos) - Various repos

## Research and Articles

### Technical Deep Dives

- [WoW Internals](https://github.com/gtker/wow_messages/wiki) - Protocol documentation
- [macOS Metal Renderer](https://www.youtube.com/watch?v=UiJJqB9nRzE) - WoW's Metal implementation
- [WoW 64-bit Client](https://us.forums.blizzard.com/en/wow/t/wow-64-bit-client/17859) - Architecture changes

## Related Projects

### wow.export

- [wow.export](https://github.com/Kruithne/wow.export) - WoW file explorer

## File Format Specifications

### Compression

- [zlib](https://www.zlib.net/) - Compression library
- [bzip2](https://www.sourceware.org/bzip2/) - Alternative compression
- [LZMA SDK](https://www.7-zip.org/sdk.html) - LZMA compression

## Learning Resources

### Videos

- [WoW Modding YouTube](https://www.youtube.com/results?search_query=wow+modding) - Video tutorials
- [Machinima Techniques](https://www.youtube.com/watch?v=VQ-nFqfSTeE) - Advanced techniques

## Development Tools

### Hex Editors

- [HxD](https://mh-nexus.de/en/hxd/) - Free hex editor (Windows)
- [Hex Fiend](https://hexfiend.com/) - macOS hex editor
- [Bless](https://github.com/afrantzis/bless) - Linux hex editor

### 3D Tools

- [Blender](https://www.blender.org/) - 3D modeling (with WoW plugins)

### Debugging

- [RenderDoc](https://renderdoc.org/) - Graphics debugger
- [PIX](https://devblogs.microsoft.com/pix/) - Windows performance tuning
- [Valgrind](https://valgrind.org/) - Memory debugging

## Historical Resources

### Classic WoW

- [Vanilla WoW Archive](https://github.com/vmangos/core) - 1.12 server

## Legal Resources

### Modding Policies

- [Blizzard Legal](https://www.blizzard.com/en-us/legal) - Terms of service
- [WoW EULA](https://www.blizzard.com/en-us/legal/fba4d00f-c7e4-4883-b8b9-1b4500a402ea/world-of-warcraft-end-user-license-agreement) - End user license

## Contributing

Know of a helpful resource not listed here? Please submit a PR to add it!

## Disclaimer

`warcraft-rs` is not affiliated with or endorsed by Blizzard Entertainment.
World of Warcraft® and Blizzard Entertainment® are trademarks or registered
trademarks of Blizzard Entertainment, Inc. in the U.S. and/or other countries.
