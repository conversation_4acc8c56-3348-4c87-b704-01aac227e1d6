# 🗺️ Map IDs Reference

Quick reference for World of Warcraft map IDs and their properties.

## Major Continents

| ID | Name | Introduced | Grid Size | Notes |
|----|------|------------|-----------|-------|
| 0 | Eastern Kingdoms | Classic | 64×64 | Azeroth |
| 1 | Kalimdor | Classic | 64×64 | Kalimdor |
| 530 | Outland | TBC | 64×64 | Expansion01 |
| 571 | Northrend | WotLK | 64×64 | Northrend |
| 646 | Deepholm | Cataclysm | 64×64 | Deephome |
| 860 | The Wandering Isle | MoP | 64×64 | NewRaceStartZone |
| 870 | Pandaria | MoP | 64×64 | HawaiiMainLand |

## Classic Instances

| ID | Name | Type | Location |
|----|------|------|----------|
| 30 | Alterac Valley | Battleground | Alterac Mountains |
| 33 | Shadowfang Keep | Dungeon | Silverpine Forest |
| 34 | The Stockade | Dungeon | Stormwind |
| 36 | Deadmines | Dungeon | Westfall |
| 43 | Wailing Caverns | Dungeon | The Barrens |
| 47 | Razorfen Kraul | Dungeon | The Barrens |
| 48 | Blackfathom Deeps | Dungeon | Ashenvale |
| 70 | Uldaman | Dungeon | Badlands |
| 90 | Gnomeregan | Dungeon | Dun Morogh |
| 109 | Sunken Temple | Dungeon | Swamp of Sorrows |
| 129 | Razorfen Downs | Dungeon | Thousand Needles |
| 189 | Scarlet Monastery | Dungeon | Tirisfal Glades |
| 209 | Zul'Farrak | Dungeon | Tanaris |
| 229 | Blackrock Spire | Dungeon | Blackrock Mountain |
| 230 | Blackrock Depths | Dungeon | Blackrock Mountain |
| 249 | Onyxia's Lair | Raid | Dustwallow Marsh |
| 309 | Zul'Gurub | Raid | Stranglethorn Vale |
| 349 | Maraudon | Dungeon | Desolace |
| 369 | Deeprun Tram | Transport | Ironforge ↔ Stormwind |
| 389 | Ragefire Chasm | Dungeon | Orgrimmar |
| 409 | Molten Core | Raid | Blackrock Mountain |
| 429 | Dire Maul | Dungeon | Feralas |
| 469 | Blackwing Lair | Raid | Blackrock Mountain |
| 489 | Warsong Gulch | Battleground | Ashenvale/Barrens |
| 509 | Ruins of Ahn'Qiraj | Raid | Silithus |
| 529 | Arathi Basin | Battleground | Arathi Highlands |
| 531 | Temple of Ahn'Qiraj | Raid | Silithus |
| 533 | Naxxramas | Raid | Eastern Plaguelands |

## The Burning Crusade

| ID | Name | Type | Location |
|----|------|------|----------|
| 532 | Karazhan | Raid | Deadwind Pass |
| 534 | The Battle for Mount Hyjal | Raid | Caverns of Time |
| 540 | The Shattered Halls | Dungeon | Hellfire Citadel |
| 542 | The Blood Furnace | Dungeon | Hellfire Citadel |
| 543 | Hellfire Ramparts | Dungeon | Hellfire Citadel |
| 544 | Magtheridon's Lair | Raid | Hellfire Citadel |
| 545 | The Steamvault | Dungeon | Coilfang Reservoir |
| 546 | The Underbog | Dungeon | Coilfang Reservoir |
| 547 | The Slave Pens | Dungeon | Coilfang Reservoir |
| 548 | Serpentshrine Cavern | Raid | Coilfang Reservoir |
| 550 | Tempest Keep | Raid | Netherstorm |
| 552 | The Arcatraz | Dungeon | Tempest Keep |
| 553 | The Botanica | Dungeon | Tempest Keep |
| 554 | The Mechanar | Dungeon | Tempest Keep |
| 555 | Shadow Labyrinth | Dungeon | Auchindoun |
| 556 | Sethekk Halls | Dungeon | Auchindoun |
| 557 | Mana-Tombs | Dungeon | Auchindoun |
| 558 | Auchenai Crypts | Dungeon | Auchindoun |
| 559 | Nagrand Arena | Arena | Nagrand |
| 560 | Old Hillsbrad Foothills | Dungeon | Caverns of Time |
| 562 | Blade's Edge Arena | Arena | Blade's Edge |
| 564 | Black Temple | Raid | Shadowmoon Valley |
| 565 | Gruul's Lair | Raid | Blade's Edge |
| 566 | Eye of the Storm | Battleground | Netherstorm |
| 568 | Zul'Aman | Raid | Ghostlands |
| 572 | Ruins of Lordaeron | Arena | Undercity |
| 580 | Sunwell Plateau | Raid | Isle of Quel'Danas |
| 585 | Magisters' Terrace | Dungeon | Isle of Quel'Danas |

## Wrath of the Lich King

| ID | Name | Type | Location |
|----|------|------|----------|
| 574 | Utgarde Keep | Dungeon | Howling Fjord |
| 575 | Utgarde Pinnacle | Dungeon | Howling Fjord |
| 576 | The Nexus | Dungeon | Borean Tundra |
| 578 | The Oculus | Dungeon | Borean Tundra |
| 595 | The Culling of Stratholme | Dungeon | Caverns of Time |
| 599 | Halls of Stone | Dungeon | Storm Peaks |
| 600 | Drak'Tharon Keep | Dungeon | Grizzly Hills |
| 601 | Azjol-Nerub | Dungeon | Dragonblight |
| 602 | Halls of Lightning | Dungeon | Storm Peaks |
| 603 | Ulduar | Raid | Storm Peaks |
| 604 | Gundrak | Dungeon | Zul'Drak |
| 607 | Strand of the Ancients | Battleground | Dragonblight |
| 608 | Violet Hold | Dungeon | Dalaran |
| 615 | The Obsidian Sanctum | Raid | Dragonblight |
| 616 | The Eye of Eternity | Raid | Borean Tundra |
| 617 | Dalaran Sewers | Arena | Dalaran |
| 618 | The Ring of Valor | Arena | Orgrimmar |
| 619 | Ahn'kahet: The Old Kingdom | Dungeon | Dragonblight |
| 624 | Vault of Archavon | Raid | Wintergrasp |
| 628 | Isle of Conquest | Battleground | Icecrown |
| 631 | Icecrown Citadel | Raid | Icecrown |
| 632 | The Forge of Souls | Dungeon | Icecrown |
| 649 | Trial of the Crusader | Raid | Icecrown |
| 650 | Trial of the Champion | Dungeon | Icecrown |
| 658 | Pit of Saron | Dungeon | Icecrown |
| 668 | Halls of Reflection | Dungeon | Icecrown |
| 724 | The Ruby Sanctum | Raid | Dragonblight |

## Map Properties

### File Structure

```text
World/
└── Maps/
    └── [MapName]/
        ├── [MapName].wdt          # World table
        ├── [MapName].wdl          # Low-res data
        ├── [MapName]_[X]_[Y].adt  # Terrain tiles
        └── [MapName].tex          # Texture list
```

### Common Map Names

| ID | Internal Name | File Path |
|----|---------------|-----------|
| 0 | Azeroth | World/Maps/Azeroth/ |
| 1 | Kalimdor | World/Maps/Kalimdor/ |
| 530 | Expansion01 | World/Maps/Expansion01/ |
| 571 | Northrend | World/Maps/Northrend/ |

### Map Flags (from Map.dbc)

```rust
bitflags! {
    pub struct MapFlags: u32 {
        const INSTANCE = 0x1;        // Is instance
        const RAID = 0x2;            // Is raid
        const PVP = 0x4;             // Is PvP
        const ARENA = 0x8;           // Is arena
        const TESTING = 0x10;        // Testing map
        const BATTLEGROUND = 0x20;   // Is battleground
        const DEVELOPMENT = 0x40;    // Dev map
        const DUNGEON = 0x100;       // Is dungeon
    }
}
```

## Usage Example

```rust
use warcraft_rs::dbc::{MapRecord, DbcTable};

// Load Map.dbc
let maps = DbcTable::<MapRecord>::open("DBFilesClient/Map.dbc")?;

// Find Eastern Kingdoms
if let Some(ek) = maps.find_by_id(0) {
    println!("Map: {}", ek.name());
    println!("Directory: {}", ek.directory);
    println!("Instance Type: {}", ek.instance_type);

    // Check if it's a continent
    if ek.instance_type == 0 {
        println!("This is a continent map");
    }
}

// List all battlegrounds
let battlegrounds = maps.iter()
    .filter(|map| map.is_battleground())
    .collect::<Vec<_>>();

println!("Found {} battlegrounds", battlegrounds.len());
```

## Notes

- Map IDs are globally unique
- Instance maps use different coordinate systems
- Some maps have multiple versions (e.g., phased zones)
- Transport maps (ships, zeppelins) are separate maps
- Map IDs above 1000 are typically test/GM maps

## See Also

- [Coordinate Systems](coordinates.md)
- [WDT Format](../formats/world-data/wdt.md)
- [DBC Format](../formats/database/dbc.md)
