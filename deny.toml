# Configuration for cargo-deny

[graph]
targets = []

[advisories]
version = 2
yanked = "deny"

[licenses]
version = 2
allow = [
    "MIT",
    "Apache-2.0",
    "Apache-2.0 WITH LLVM-exception",
    "BSD-3-Clause",
    "BSD-2-Clause",
    "ISC",
    "Unicode-3.0",
    "Unicode-DFS-2016",
    "CC0-1.0",
    "MPL-2.0",
    "Zlib",
    "bzip2-1.0.6",
    "NCSA",
]

[bans]
multiple-versions = "warn"
deny = []
skip = [
    # These duplicates are from external dependencies we can't control
    { name = "bitflags", version = "1.3.2" },  # Used by png/image
    { name = "thiserror", version = "1.0" },   # Used by rav1e
    { name = "thiserror-impl", version = "1.0" },
    { name = "rand", version = "0.8" },        # Used by rsa
    { name = "rand_core", version = "0.6" },
    { name = "rand_chacha", version = "0.3" },
    { name = "getrandom", version = "0.2" },
    { name = "unicode-width", version = "0.1" }, # Used by prettytable-rs
    { name = "windows-sys", version = "0.59" },
    { name = "windows-targets", version = "0.52" },
    { name = "windows_x86_64_gnu", version = "0.52" },
    { name = "windows_x86_64_msvc", version = "0.52" },
]

[sources]
unknown-registry = "warn"
unknown-git = "warn"
allow-registry = ["https://github.com/rust-lang/crates.io-index"]
allow-git = []