---
name: Cleanup caches for closed PRs

on:
  # Run twice every day to remove the cache so that the caches from the closed prs
  # are removed.
  schedule:
    - cron: '0 17 * * *'
    - cron: '30 18 * * *'
  workflow_dispatch:

jobs:
  cleanup:
    name: Clear all GitHub action caches
    runs-on: ubuntu-latest
    permissions: write-all

    steps:
      - name: Clear all caches
        run: |
          echo "Fetching list of cache key"
          cacheKeysForPR=$(gh cache list --limit 1000 --json id --jq '.[].id')

          ## Setting this to not fail the workflow while deleting cache keys.
          set +e
          echo "Deleting caches..."
          for cacheKey in $cacheKeysForPR
          do
              gh cache delete $cacheKey
          done
          echo "Done"
        env:
          GH_TOKEN: ${{ github.token }}
          GH_REPO: ${{ github.repository }}
