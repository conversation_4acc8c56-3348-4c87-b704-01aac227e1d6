name: ❓ Question or Support
description: Ask a question or request help
title: "[Question]: "
labels: ["question", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for your question! Please provide as much context as possible to help us give you the best answer.
        
        **Before asking:**
        - Check the [documentation](../docs/) and [README](../README.md)
        - Search existing issues and discussions
        - Review the [Getting Started guide](../docs/getting-started/)

  - type: checkboxes
    id: prerequisites
    attributes:
      label: Prerequisites
      description: Please confirm you have completed the following
      options:
        - label: I have searched existing issues and discussions
          required: true
        - label: I have read the relevant documentation
          required: true
        - label: This is not a bug report or feature request
          required: true

  - type: dropdown
    id: question-type
    attributes:
      label: Question Type
      description: What type of question is this?
      options:
        - Usage/How-to
        - Implementation guidance
        - Best practices
        - Architecture/design
        - Performance
        - Compatibility
        - Integration
        - Troubleshooting
        - Development/contributing
        - Other
    validations:
      required: true

  - type: textarea
    id: question
    attributes:
      label: Question
      description: What would you like to know?
      placeholder: |
        Please be specific and provide context...
        What are you trying to accomplish?
        What have you tried so far?
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: Context
      description: Provide relevant context for your question
      placeholder: |
        - What are you building/working on?
        - What's your experience level with Rust/game development?
        - Are you working with specific WoW versions or file types?
        - Any constraints or requirements?
    validations:
      required: false

  - type: textarea
    id: current-approach
    attributes:
      label: Current Approach
      description: What have you tried so far?
      placeholder: |
        - Code examples you've attempted
        - Commands you've run
        - Research you've done
        - Similar solutions you've found
    validations:
      required: false

  - type: checkboxes
    id: related-components
    attributes:
      label: Related Components
      description: Which components is your question about? (Check all that apply)
      options:
        - label: MPQ archives
        - label: DBC/DB2 databases
        - label: BLP textures
        - label: M2 models
        - label: WMO world objects
        - label: ADT terrain
        - label: WDT/WDL world data
        - label: CLI interface
        - label: FFI/C bindings
        - label: Build system/development
        - label: General architecture
        - label: Performance optimization

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of warcraft-rs are you using?
      placeholder: "v0.1.0 or commit hash"
    validations:
      required: false

  - type: dropdown
    id: operating-system
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - Linux
        - macOS
        - Windows
        - Other
        - Not applicable
    validations:
      required: false

  - type: textarea
    id: code-examples
    attributes:
      label: Code Examples
      description: Any relevant code you're working with
      render: rust
      placeholder: |
        ```rust
        // Your code here...
        use wow_mpq::Archive;
        
        fn main() -> Result<(), Box<dyn std::error::Error>> {
            let archive = Archive::open("example.mpq")?;
            // What you're trying to do...
            Ok(())
        }
        ```
    validations:
      required: false

  - type: textarea
    id: expected-outcome
    attributes:
      label: Expected Outcome
      description: What result or behavior are you hoping to achieve?
      placeholder: |
        Describe what you want to accomplish...
        What would success look like?
    validations:
      required: false

  - type: textarea
    id: additional-info
    attributes:
      label: Additional Information
      description: Any other relevant information
      placeholder: |
        - Links to external resources
        - Screenshots or examples
        - Specific file formats or versions
        - Performance requirements
        - Timeline considerations
    validations:
      required: false