# Handle line endings automatically for files detected as text
# and leave all files detected as binary untouched.
* text=auto

#
# The above will handle all files NOT found below
#
# These files are text and should be normalized (Convert crlf => lf)
*.gitattributes text
.gitignore text
*.md text

#
# The following files are Windows specific and will thus be allowed
# in crlf form
*.ps1 text eol=crlf
*.bat text eol=crlf
*.cmd text eol=crlf
*.inf text eol=crlf

#
# This will allow git to show diffs using GPG.
*.gpg filter=gpg diff=gpg
*.asc filter=gpg diff=gpg
