# Changelog

All notable changes to wow-wmo will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.3.0] - 2025-08-07

### Changed

- Version bump to 0.3.0 for coordinated workspace release
- Updated dependencies and documentation

## [0.2.0] - 2025-06-28

### Added

- Initial release of wow-wmo crate
- Support for WMO (World Map Object) root files
- Support for WMO group files
- Material parsing with texture references
- Portal and portal reference support
- Lighting data (MOLT, MOLS chunks)
- Doodad sets and instances (MODS, MODD)
- Vertex and triangle data for groups
- BSP tree support for collision
- Fog definitions
- Liquid data support
- WMO validation and visualization tools
