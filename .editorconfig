; top-most EditorConfig file
root = true

; Unix-style newlines
[*]
charset = utf-8
end_of_line = lf
indent_size = 4
indent_style = space
insert_final_newline = true
trim_trailing_whitespace = true

[*.md]
indent_size = 2
trim_trailing_whitespace = false

[{Makefile,justfile,*.bash,*.sh}]
indent_style = tab

[*.{ps1,bat,cmd,inf}]
indent_style = tab
end_of_line = crlf

[*.{json,toml,yml,yaml,xml}]
indent_size = 2

[*.{js,jsx,php,ts,tsx,twig}]
indent_size = 4

[*.{html,css}]
indent_size = 4

[{Cap<PERSON>le,Gemfile,*.rb,*.rake}]
indent_size = 2

[*.rs]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{hcl,tf,tfvars}]
indent_size = 2

[.pass-template]
indent_size = 2
